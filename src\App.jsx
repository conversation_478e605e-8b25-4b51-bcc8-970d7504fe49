import { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [systemStatus, setSystemStatus] = useState({
    thermalCamera1: { status: "active", temperature: 45.2, alert: false },
    thermalCamera2: { status: "active", temperature: 42.8, alert: false },
    ocrCamera: { status: "active", lastScan: "TRP-001", confidence: 98.5 },
    torpedoCar: { position: "Station A", speed: 0, temperature: 850 },
    serverPC: { status: "online", cpu: 45, memory: 67, storage: 78 },
    alarmSystem: { active: false, lastAlert: null },
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus((prev) => ({
        ...prev,
        thermalCamera1: {
          ...prev.thermalCamera1,
          temperature: 40 + Math.random() * 20,
          alert: Math.random() > 0.8,
        },
        thermalCamera2: {
          ...prev.thermalCamera2,
          temperature: 38 + Math.random() * 15,
          alert: Math.random() > 0.85,
        },
        torpedoCar: {
          ...prev.torpedoCar,
          temperature: 800 + Math.random() * 100,
        },
        serverPC: {
          ...prev.serverPC,
          cpu: 30 + Math.random() * 40,
          memory: 50 + Math.random() * 30,
        },
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      {/* Main System Layout - Exact replica of the diagram */}
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Thermal Monitoring System
        </h1>

        {/* System Diagram Layout */}
        <div className="relative bg-white border-4 border-gray-800 rounded-lg p-8 min-h-[600px]">
          {/* OCR Camera - Top Left */}
          <div className="absolute top-4 left-4">
            <div className="bg-gray-200 border-2 border-gray-600 rounded-lg p-3 w-32">
              <div className="text-xs font-semibold text-gray-700 mb-2">
                OCR Camera
                <br />
                for
                <br />
                identification
              </div>
              <div className="bg-gray-400 h-16 w-16 mx-auto mb-2 rounded border-2 border-gray-600 flex items-center justify-center relative">
                <div className="bg-gray-600 h-8 w-8 rounded"></div>
                {/* Live data overlay */}
                <div className="absolute -bottom-6 left-0 right-0 text-center">
                  <div className="bg-blue-600 text-white text-xs px-1 py-0.5 rounded">
                    {systemStatus.ocrCamera.lastScan}
                  </div>
                </div>
              </div>
              {/* Wireless signal */}
              <div className="flex justify-center mt-2">
                <div
                  className={`text-lg ${
                    systemStatus.ocrCamera.status === "active"
                      ? "text-green-500"
                      : "text-red-500"
                  }`}
                >
                  📶
                </div>
              </div>
            </div>
          </div>

          {/* Thermal Camera - Top Center */}
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <div className="bg-gray-200 border-2 border-gray-600 rounded-lg p-3 w-32">
              <div className="text-xs font-semibold text-gray-700 mb-2 text-center">
                Thermal Camera
              </div>
              <div className="bg-gray-400 h-16 w-16 mx-auto rounded border-2 border-gray-600 flex items-center justify-center relative">
                <div
                  className={`h-8 w-8 rounded ${
                    systemStatus.thermalCamera1.alert
                      ? "bg-red-600"
                      : "bg-gray-600"
                  }`}
                ></div>
                {/* Live temperature overlay */}
                <div className="absolute -bottom-6 left-0 right-0 text-center">
                  <div
                    className={`text-white text-xs px-1 py-0.5 rounded ${
                      systemStatus.thermalCamera1.alert
                        ? "bg-red-600"
                        : "bg-orange-600"
                    }`}
                  >
                    {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Server PC - Top Right */}
          <div className="absolute top-4 right-4">
            <div className="bg-gray-200 border-2 border-gray-600 rounded-lg p-3 w-40">
              <div className="bg-gray-300 h-20 w-32 mx-auto rounded border border-gray-500 flex flex-col items-center justify-center relative">
                <div
                  className={`w-6 h-6 rounded mb-1 flex items-center justify-center text-white text-xs font-bold ${
                    systemStatus.serverPC.status === "online"
                      ? "bg-blue-500"
                      : "bg-red-500"
                  }`}
                >
                  DB
                </div>
                <div className="text-xs text-gray-700">
                  Server PC with software
                </div>
                {/* Live server stats */}
                <div className="absolute -bottom-8 left-0 right-0 text-center">
                  <div className="bg-gray-700 text-white text-xs px-1 py-0.5 rounded">
                    CPU: {systemStatus.serverPC.cpu.toFixed(0)}%
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Torpedo Car - Center */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="text-center mb-2">
              <div className="text-sm font-semibold text-gray-700">
                Torpedo Car
              </div>
              {/* Live torpedo data */}
              <div className="text-xs text-gray-600">
                Temp: {systemStatus.torpedoCar.temperature.toFixed(0)}°C |
                Position: {systemStatus.torpedoCar.position}
              </div>
            </div>
            <div className="flex items-center">
              {/* Left track */}
              <div className="bg-gray-600 h-8 w-16 flex items-center justify-center">
                <div className="flex space-x-1">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="w-1 h-6 bg-gray-400"></div>
                  ))}
                </div>
              </div>

              {/* Red indicator */}
              <div className="bg-red-600 w-8 h-8 flex items-center justify-center">
                <div className="bg-red-800 w-4 h-4 rounded"></div>
              </div>

              {/* Main torpedo body */}
              <div className="bg-gray-500 h-12 w-48 relative flex items-center justify-center">
                <div
                  className={`h-8 w-32 rounded ${
                    systemStatus.torpedoCar.temperature > 900
                      ? "bg-gradient-to-r from-red-500 to-orange-600"
                      : "bg-gradient-to-r from-orange-400 to-yellow-500"
                  }`}
                ></div>
                {/* Temperature indicator on torpedo */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6">
                  <div className="bg-orange-600 text-white text-xs px-1 py-0.5 rounded">
                    {systemStatus.torpedoCar.temperature.toFixed(0)}°C
                  </div>
                </div>
              </div>

              {/* Right connector */}
              <div className="bg-gray-600 w-12 h-8"></div>

              {/* Right track */}
              <div className="bg-gray-600 h-8 w-16 flex items-center justify-center">
                <div className="flex space-x-1">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="w-1 h-6 bg-gray-400"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Second Thermal Camera - Bottom Center */}
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
            <div className="bg-gray-200 border-2 border-gray-600 rounded-lg p-3 w-32">
              <div className="text-xs font-semibold text-gray-700 mb-2 text-center">
                Thermal Camera
              </div>
              <div className="bg-gray-400 h-16 w-16 mx-auto rounded border-2 border-gray-600 flex items-center justify-center relative">
                <div
                  className={`h-8 w-8 rounded ${
                    systemStatus.thermalCamera2.alert
                      ? "bg-red-600"
                      : "bg-gray-600"
                  }`}
                ></div>
                {/* Live temperature overlay */}
                <div className="absolute -bottom-6 left-0 right-0 text-center">
                  <div
                    className={`text-white text-xs px-1 py-0.5 rounded ${
                      systemStatus.thermalCamera2.alert
                        ? "bg-red-600"
                        : "bg-orange-600"
                    }`}
                  >
                    {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Control Room - Bottom Right */}
          <div className="absolute bottom-4 right-4">
            <div className="bg-white border-4 border-gray-800 rounded-lg p-4 w-80">
              <div className="border-t-4 border-gray-600 pt-2">
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-gray-200 rounded-full w-8 h-8 border-2 border-gray-600"></div>
                  <div className="flex-1 mx-4">
                    <div className="bg-gray-100 border border-gray-400 rounded px-3 py-1 text-sm">
                      Status:{" "}
                      {systemStatus.alarmSystem.active
                        ? "ALARM ACTIVE"
                        : "System Normal"}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-center">
                    <div className="text-xs font-semibold text-gray-700 mb-2">
                      ALARM/HOOTER
                    </div>
                    <button
                      onClick={() =>
                        setSystemStatus((prev) => ({
                          ...prev,
                          alarmSystem: {
                            ...prev.alarmSystem,
                            active: !prev.alarmSystem.active,
                          },
                        }))
                      }
                      className={`px-2 py-1 rounded text-xs font-bold ${
                        systemStatus.alarmSystem.active
                          ? "bg-red-600 text-white animate-pulse"
                          : "bg-yellow-500 text-black"
                      }`}
                    >
                      {systemStatus.alarmSystem.active ? "STOP" : "TRIGGER"}
                    </button>
                  </div>

                  <div className="text-center">
                    <div className="bg-gray-200 border-2 border-gray-600 rounded p-2 w-16 h-16 flex items-center justify-center mb-2">
                      <div
                        className={`w-8 h-8 rounded ${
                          systemStatus.serverPC.status === "online"
                            ? "bg-blue-400"
                            : "bg-red-400"
                        }`}
                      ></div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="bg-gray-300 w-8 h-2 rounded"></div>
                      <div
                        className={`w-6 h-6 rounded-full ${
                          systemStatus.alarmSystem.active
                            ? "bg-red-400 animate-pulse"
                            : "bg-blue-400"
                        }`}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="text-center mt-2">
                  <div className="text-xs font-semibold text-gray-700">
                    CONTROL
                    <br />
                    ROOM
                  </div>
                  <div className="text-xs font-semibold text-gray-700 mt-2">
                    Operator
                  </div>
                  {/* Live system stats */}
                  <div className="text-xs text-gray-600 mt-1">
                    Alerts:{" "}
                    {(systemStatus.thermalCamera1.alert ? 1 : 0) +
                      (systemStatus.thermalCamera2.alert ? 1 : 0)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Connection lines */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            {/* Line from Server PC to Control Room */}
            <line
              x1="75%"
              y1="25%"
              x2="75%"
              y2="75%"
              stroke="#374151"
              strokeWidth="2"
            />

            {/* Line from Torpedo to Control Room */}
            <line
              x1="50%"
              y1="60%"
              x2="75%"
              y2="75%"
              stroke="#374151"
              strokeWidth="2"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

export default App;
