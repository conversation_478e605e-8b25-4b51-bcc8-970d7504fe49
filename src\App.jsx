import { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [systemStatus, setSystemStatus] = useState({
    thermalCamera1: { status: "active", temperature: 45.2, alert: false },
    thermalCamera2: { status: "active", temperature: 42.8, alert: false },
    ocrCamera: { status: "active", lastScan: "TRP-001", confidence: 98.5 },
    torpedoCar: { position: "Station A", speed: 0, temperature: 850 },
    serverPC: { status: "online", cpu: 45, memory: 67, storage: 78 },
    alarmSystem: { active: false, lastAlert: null },
  });

  const [selectedView, setSelectedView] = useState("overview");

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus((prev) => ({
        ...prev,
        thermalCamera1: {
          ...prev.thermalCamera1,
          temperature: 40 + Math.random() * 20,
          alert: Math.random() > 0.8,
        },
        thermalCamera2: {
          ...prev.thermalCamera2,
          temperature: 38 + Math.random() * 15,
          alert: Math.random() > 0.85,
        },
        torpedoCar: {
          ...prev.torpedoCar,
          temperature: 800 + Math.random() * 100,
        },
        serverPC: {
          ...prev.serverPC,
          cpu: 30 + Math.random() * 40,
          memory: 50 + Math.random() * 30,
        },
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-blue-400">
            Thermal Monitoring System
          </h1>
          <div className="flex space-x-4">
            <button
              onClick={() => setSelectedView("overview")}
              className={`px-4 py-2 rounded ${
                selectedView === "overview" ? "bg-blue-600" : "bg-gray-600"
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setSelectedView("cameras")}
              className={`px-4 py-2 rounded ${
                selectedView === "cameras" ? "bg-blue-600" : "bg-gray-600"
              }`}
            >
              Cameras
            </button>
            <button
              onClick={() => setSelectedView("control")}
              className={`px-4 py-2 rounded ${
                selectedView === "control" ? "bg-blue-600" : "bg-gray-600"
              }`}
            >
              Control Room
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        {selectedView === "overview" && (
          <OverviewDashboard systemStatus={systemStatus} />
        )}
        {selectedView === "cameras" && (
          <CameraView systemStatus={systemStatus} />
        )}
        {selectedView === "control" && (
          <ControlRoom
            systemStatus={systemStatus}
            setSystemStatus={setSystemStatus}
          />
        )}
      </main>
    </div>
  );
}

// Overview Dashboard Component
function OverviewDashboard({ systemStatus }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* System Status Cards */}
      <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Thermal Camera 1 */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">Thermal Camera 1</h3>
            <div
              className={`w-3 h-3 rounded-full ${
                systemStatus.thermalCamera1.status === "active"
                  ? "bg-green-500"
                  : "bg-red-500"
              }`}
            ></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Temperature:</span>
              <span
                className={`font-mono ${
                  systemStatus.thermalCamera1.alert
                    ? "text-red-400"
                    : "text-green-400"
                }`}
              >
                {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
              </span>
            </div>
            {systemStatus.thermalCamera1.alert && (
              <div className="text-red-400 text-sm">
                ⚠️ High temperature detected
              </div>
            )}
          </div>
        </div>

        {/* Thermal Camera 2 */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">Thermal Camera 2</h3>
            <div
              className={`w-3 h-3 rounded-full ${
                systemStatus.thermalCamera2.status === "active"
                  ? "bg-green-500"
                  : "bg-red-500"
              }`}
            ></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Temperature:</span>
              <span
                className={`font-mono ${
                  systemStatus.thermalCamera2.alert
                    ? "text-red-400"
                    : "text-green-400"
                }`}
              >
                {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
              </span>
            </div>
            {systemStatus.thermalCamera2.alert && (
              <div className="text-red-400 text-sm">
                ⚠️ High temperature detected
              </div>
            )}
          </div>
        </div>

        {/* OCR Camera */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">OCR Camera</h3>
            <div
              className={`w-3 h-3 rounded-full ${
                systemStatus.ocrCamera.status === "active"
                  ? "bg-green-500"
                  : "bg-red-500"
              }`}
            ></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Last Scan:</span>
              <span className="font-mono text-blue-400">
                {systemStatus.ocrCamera.lastScan}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Confidence:</span>
              <span className="font-mono text-green-400">
                {systemStatus.ocrCamera.confidence}%
              </span>
            </div>
          </div>
        </div>

        {/* Server PC */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">Server PC</h3>
            <div
              className={`w-3 h-3 rounded-full ${
                systemStatus.serverPC.status === "online"
                  ? "bg-green-500"
                  : "bg-red-500"
              }`}
            ></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>CPU:</span>
              <span className="font-mono text-yellow-400">
                {systemStatus.serverPC.cpu.toFixed(0)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Memory:</span>
              <span className="font-mono text-yellow-400">
                {systemStatus.serverPC.memory.toFixed(0)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Storage:</span>
              <span className="font-mono text-yellow-400">
                {systemStatus.serverPC.storage}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Torpedo Car Visualization */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">Torpedo Car Status</h3>
        <div className="space-y-4">
          <div className="flex justify-between">
            <span>Position:</span>
            <span className="font-mono text-blue-400">
              {systemStatus.torpedoCar.position}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Speed:</span>
            <span className="font-mono text-green-400">
              {systemStatus.torpedoCar.speed} m/s
            </span>
          </div>
          <div className="flex justify-between">
            <span>Temperature:</span>
            <span className="font-mono text-orange-400">
              {systemStatus.torpedoCar.temperature.toFixed(0)}°C
            </span>
          </div>

          {/* Visual representation */}
          <div className="mt-4 p-4 bg-gray-700 rounded">
            <div className="flex items-center justify-center">
              <div className="w-16 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <span className="text-xs font-bold">🚂</span>
              </div>
            </div>
            <div className="mt-2 text-center text-sm text-gray-400">
              Torpedo Car
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Camera View Component
function CameraView({ systemStatus }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Thermal Camera 1 Feed */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Thermal Camera 1</h3>
          <div
            className={`px-2 py-1 rounded text-xs ${
              systemStatus.thermalCamera1.status === "active"
                ? "bg-green-600"
                : "bg-red-600"
            }`}
          >
            {systemStatus.thermalCamera1.status.toUpperCase()}
          </div>
        </div>

        {/* Simulated thermal camera feed */}
        <div className="aspect-video bg-gradient-to-br from-blue-900 via-purple-900 to-red-900 rounded-lg mb-4 relative overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-32 h-24 bg-gradient-to-r from-yellow-400 to-red-600 rounded-lg mx-auto mb-2 opacity-80"></div>
              <div className="text-sm text-gray-300">Live Thermal Feed</div>
            </div>
          </div>
          {/* Temperature overlay */}
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 rounded px-2 py-1 text-xs">
            {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Resolution:</span>
            <div className="font-mono">640x480</div>
          </div>
          <div>
            <span className="text-gray-400">FPS:</span>
            <div className="font-mono">30</div>
          </div>
          <div>
            <span className="text-gray-400">Range:</span>
            <div className="font-mono">-20°C to 150°C</div>
          </div>
          <div>
            <span className="text-gray-400">Accuracy:</span>
            <div className="font-mono">±2°C</div>
          </div>
        </div>
      </div>

      {/* Thermal Camera 2 Feed */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Thermal Camera 2</h3>
          <div
            className={`px-2 py-1 rounded text-xs ${
              systemStatus.thermalCamera2.status === "active"
                ? "bg-green-600"
                : "bg-red-600"
            }`}
          >
            {systemStatus.thermalCamera2.status.toUpperCase()}
          </div>
        </div>

        {/* Simulated thermal camera feed */}
        <div className="aspect-video bg-gradient-to-br from-blue-900 via-green-900 to-yellow-900 rounded-lg mb-4 relative overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-32 h-24 bg-gradient-to-r from-green-400 to-yellow-600 rounded-lg mx-auto mb-2 opacity-80"></div>
              <div className="text-sm text-gray-300">Live Thermal Feed</div>
            </div>
          </div>
          {/* Temperature overlay */}
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 rounded px-2 py-1 text-xs">
            {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Resolution:</span>
            <div className="font-mono">640x480</div>
          </div>
          <div>
            <span className="text-gray-400">FPS:</span>
            <div className="font-mono">30</div>
          </div>
          <div>
            <span className="text-gray-400">Range:</span>
            <div className="font-mono">-20°C to 150°C</div>
          </div>
          <div>
            <span className="text-gray-400">Accuracy:</span>
            <div className="font-mono">±2°C</div>
          </div>
        </div>
      </div>

      {/* OCR Camera Feed */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">OCR Camera</h3>
          <div
            className={`px-2 py-1 rounded text-xs ${
              systemStatus.ocrCamera.status === "active"
                ? "bg-green-600"
                : "bg-red-600"
            }`}
          >
            {systemStatus.ocrCamera.status.toUpperCase()}
          </div>
        </div>

        {/* Simulated OCR camera feed */}
        <div className="aspect-video bg-gray-700 rounded-lg mb-4 relative overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-32 h-16 bg-gray-600 rounded border-2 border-dashed border-gray-400 flex items-center justify-center mx-auto mb-2">
                <span className="font-mono text-lg font-bold">
                  {systemStatus.ocrCamera.lastScan}
                </span>
              </div>
              <div className="text-sm text-gray-300">OCR Recognition</div>
            </div>
          </div>
          {/* Confidence overlay */}
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 rounded px-2 py-1 text-xs">
            Confidence: {systemStatus.ocrCamera.confidence}%
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Last Scan:</span>
            <div className="font-mono text-blue-400">
              {systemStatus.ocrCamera.lastScan}
            </div>
          </div>
          <div>
            <span className="text-gray-400">Scan Rate:</span>
            <div className="font-mono">2/sec</div>
          </div>
          <div>
            <span className="text-gray-400">Accuracy:</span>
            <div className="font-mono text-green-400">
              {systemStatus.ocrCamera.confidence}%
            </div>
          </div>
          <div>
            <span className="text-gray-400">Format:</span>
            <div className="font-mono">TRP-XXX</div>
          </div>
        </div>
      </div>

      {/* System Diagram */}
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4">System Layout</h3>
        <div className="aspect-video bg-gray-700 rounded-lg p-4 relative">
          {/* Simplified system diagram */}
          <div className="absolute top-4 left-4">
            <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs">
              📷
            </div>
            <div className="text-xs mt-1">OCR</div>
          </div>

          <div className="absolute top-4 right-4">
            <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center text-xs">
              🌡️
            </div>
            <div className="text-xs mt-1">TC1</div>
          </div>

          <div className="absolute bottom-4 right-4">
            <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center text-xs">
              🌡️
            </div>
            <div className="text-xs mt-1">TC2</div>
          </div>

          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-16 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <span className="text-xs font-bold">🚂</span>
            </div>
            <div className="text-xs mt-1 text-center">Torpedo</div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Control Room Component
function ControlRoom({ systemStatus, setSystemStatus }) {
  const [alarmHistory, setAlarmHistory] = useState([
    {
      id: 1,
      time: "14:32:15",
      type: "Temperature Alert",
      message: "Thermal Camera 1 - High temperature detected",
      severity: "warning",
    },
    {
      id: 2,
      time: "14:28:42",
      type: "System Info",
      message: "OCR Camera - New torpedo identified: TRP-001",
      severity: "info",
    },
    {
      id: 3,
      time: "14:25:18",
      type: "Temperature Alert",
      message: "Thermal Camera 2 - Temperature spike detected",
      severity: "warning",
    },
  ]);

  const triggerAlarm = () => {
    setSystemStatus((prev) => ({
      ...prev,
      alarmSystem: {
        ...prev.alarmSystem,
        active: !prev.alarmSystem.active,
        lastAlert: new Date().toLocaleTimeString(),
      },
    }));

    if (!systemStatus.alarmSystem.active) {
      const newAlert = {
        id: alarmHistory.length + 1,
        time: new Date().toLocaleTimeString(),
        type: "Manual Alarm",
        message: "Operator triggered manual alarm",
        severity: "critical",
      };
      setAlarmHistory((prev) => [newAlert, ...prev]);
    }
  };

  const resetSystem = () => {
    setSystemStatus((prev) => ({
      ...prev,
      thermalCamera1: { ...prev.thermalCamera1, alert: false },
      thermalCamera2: { ...prev.thermalCamera2, alert: false },
      alarmSystem: { ...prev.alarmSystem, active: false },
    }));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Control Panel */}
      <div className="lg:col-span-2 space-y-6">
        {/* Operator Controls */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold mb-6 flex items-center">
            <span className="mr-2">👨‍💼</span>
            Operator Control Panel
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Alarm Controls */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-blue-400">
                Alarm System
              </h4>
              <div className="space-y-3">
                <button
                  onClick={triggerAlarm}
                  className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
                    systemStatus.alarmSystem.active
                      ? "bg-red-600 hover:bg-red-700 text-white"
                      : "bg-yellow-600 hover:bg-yellow-700 text-white"
                  }`}
                >
                  {systemStatus.alarmSystem.active
                    ? "🚨 STOP ALARM"
                    : "⚠️ TRIGGER ALARM"}
                </button>

                <button
                  onClick={resetSystem}
                  className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  🔄 Reset System
                </button>
              </div>
            </div>

            {/* System Status */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-green-400">
                System Status
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-gray-700 rounded">
                  <span>Thermal Camera 1:</span>
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      systemStatus.thermalCamera1.status === "active"
                        ? "bg-green-600"
                        : "bg-red-600"
                    }`}
                  >
                    {systemStatus.thermalCamera1.status.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 bg-gray-700 rounded">
                  <span>Thermal Camera 2:</span>
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      systemStatus.thermalCamera2.status === "active"
                        ? "bg-green-600"
                        : "bg-red-600"
                    }`}
                  >
                    {systemStatus.thermalCamera2.status.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 bg-gray-700 rounded">
                  <span>OCR Camera:</span>
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      systemStatus.ocrCamera.status === "active"
                        ? "bg-green-600"
                        : "bg-red-600"
                    }`}
                  >
                    {systemStatus.ocrCamera.status.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 bg-gray-700 rounded">
                  <span>Server PC:</span>
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      systemStatus.serverPC.status === "online"
                        ? "bg-green-600"
                        : "bg-red-600"
                    }`}
                  >
                    {systemStatus.serverPC.status.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Alert History */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">📋</span>
            Alert History
          </h3>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {alarmHistory.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border-l-4 ${
                  alert.severity === "critical"
                    ? "bg-red-900 border-red-500"
                    : alert.severity === "warning"
                    ? "bg-yellow-900 border-yellow-500"
                    : "bg-blue-900 border-blue-500"
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-semibold text-sm">{alert.type}</div>
                    <div className="text-sm text-gray-300">{alert.message}</div>
                  </div>
                  <div className="text-xs text-gray-400">{alert.time}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Live Monitoring */}
      <div className="space-y-6">
        {/* Current Alerts */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <span className="mr-2">🚨</span>
            Active Alerts
          </h3>

          <div className="space-y-2">
            {systemStatus.alarmSystem.active && (
              <div className="p-3 bg-red-900 border border-red-500 rounded-lg animate-pulse">
                <div className="font-semibold text-red-300">ALARM ACTIVE</div>
                <div className="text-sm text-red-400">
                  Manual alarm triggered
                </div>
              </div>
            )}

            {systemStatus.thermalCamera1.alert && (
              <div className="p-3 bg-yellow-900 border border-yellow-500 rounded-lg">
                <div className="font-semibold text-yellow-300">
                  Temperature Alert
                </div>
                <div className="text-sm text-yellow-400">
                  TC1: {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
                </div>
              </div>
            )}

            {systemStatus.thermalCamera2.alert && (
              <div className="p-3 bg-yellow-900 border border-yellow-500 rounded-lg">
                <div className="font-semibold text-yellow-300">
                  Temperature Alert
                </div>
                <div className="text-sm text-yellow-400">
                  TC2: {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
                </div>
              </div>
            )}

            {!systemStatus.alarmSystem.active &&
              !systemStatus.thermalCamera1.alert &&
              !systemStatus.thermalCamera2.alert && (
                <div className="p-3 bg-green-900 border border-green-500 rounded-lg">
                  <div className="font-semibold text-green-300">
                    All Systems Normal
                  </div>
                  <div className="text-sm text-green-400">No active alerts</div>
                </div>
              )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Current Torpedo:</span>
              <span className="font-mono text-blue-400">
                {systemStatus.ocrCamera.lastScan}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Torpedo Temp:</span>
              <span className="font-mono text-orange-400">
                {systemStatus.torpedoCar.temperature.toFixed(0)}°C
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Server CPU:</span>
              <span className="font-mono text-yellow-400">
                {systemStatus.serverPC.cpu.toFixed(0)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Server Memory:</span>
              <span className="font-mono text-yellow-400">
                {systemStatus.serverPC.memory.toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
