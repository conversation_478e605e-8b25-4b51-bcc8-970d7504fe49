import { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [systemStatus, setSystemStatus] = useState({
    thermalCamera1: { status: "active", temperature: 45.2, alert: false },
    thermalCamera2: { status: "active", temperature: 42.8, alert: false },
    ocrCamera: { status: "active", lastScan: "TRP-001", confidence: 98.5 },
    torpedoCar: { position: "Station A", speed: 0, temperature: 850 },
    serverPC: { status: "online", cpu: 45, memory: 67, storage: 78 },
    alarmSystem: { active: false, lastAlert: null },
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus((prev) => ({
        ...prev,
        thermalCamera1: {
          ...prev.thermalCamera1,
          temperature: 40 + Math.random() * 20,
          alert: Math.random() > 0.8,
        },
        thermalCamera2: {
          ...prev.thermalCamera2,
          temperature: 38 + Math.random() * 15,
          alert: Math.random() > 0.85,
        },
        torpedoCar: {
          ...prev.torpedoCar,
          temperature: 800 + Math.random() * 100,
        },
        serverPC: {
          ...prev.serverPC,
          cpu: 30 + Math.random() * 40,
          memory: 50 + Math.random() * 30,
        },
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      {/* Title */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-black">
          Thermal Monitoring System
        </h1>
      </div>

      {/* Main Container - Exact replica of the diagram */}
      <div
        className="max-w-7xl mx-auto bg-white border-4 border-gray-800 relative"
        style={{ minHeight: "600px", padding: "40px" }}
      >
        {/* OCR Camera - Top Left */}
        <div className="absolute top-12 left-12">
          <div
            className="border-2 border-gray-800 bg-gray-50 p-4"
            style={{ width: "140px" }}
          >
            <div className="text-xs font-medium text-gray-800 mb-3 leading-tight">
              OCR Camera
              <br />
              for
              <br />
              identification
            </div>
            <div
              className="bg-gray-200 border border-gray-500 mx-auto mb-3 flex items-center justify-center relative"
              style={{ width: "60px", height: "70px" }}
            >
              <div
                className="bg-gray-700 border border-gray-800"
                style={{ width: "40px", height: "50px" }}
              ></div>
              {/* Diagonal stripes pattern */}
              <div className="absolute inset-0 opacity-30">
                <div className="w-full h-full bg-gradient-to-br from-transparent via-gray-400 to-transparent transform rotate-45"></div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-blue-600 text-white text-xs px-2 py-1 font-medium">
                {systemStatus.ocrCamera.lastScan}
              </div>
            </div>
            {/* Wireless signal */}
            <div className="text-center mt-2">
              <div className="text-green-600 text-lg">📶</div>
            </div>
          </div>
        </div>

        {/* Thermal Camera - Top Center */}
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2">
          <div
            className="border-2 border-gray-800 bg-gray-50 p-4"
            style={{ width: "140px" }}
          >
            <div className="text-xs font-medium text-gray-800 mb-3 text-center">
              Thermal Camera
            </div>
            <div
              className="bg-gray-200 border border-gray-500 mx-auto mb-3 flex items-center justify-center"
              style={{ width: "60px", height: "70px" }}
            >
              <div
                className={`border border-gray-800 ${
                  systemStatus.thermalCamera1.alert
                    ? "bg-red-600"
                    : "bg-gray-700"
                }`}
                style={{ width: "40px", height: "50px" }}
              ></div>
            </div>
            <div className="text-center">
              <div
                className={`text-white text-xs px-2 py-1 font-medium ${
                  systemStatus.thermalCamera1.alert
                    ? "bg-red-600"
                    : "bg-orange-600"
                }`}
              >
                {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
              </div>
            </div>
          </div>
        </div>

        {/* Server PC - Top Right */}
        <div className="absolute top-12 right-12">
          <div
            className="border-2 border-gray-800 bg-gray-50 p-4"
            style={{ width: "160px" }}
          >
            <div
              className="bg-gray-300 border border-gray-600 mx-auto p-3 text-center relative"
              style={{ width: "140px", height: "90px" }}
            >
              {/* Monitor screen */}
              <div
                className="bg-gray-700 border border-gray-800 mx-auto mb-2 flex items-center justify-center"
                style={{ width: "100px", height: "60px" }}
              >
                <div
                  className="bg-blue-600 text-white text-xs font-bold flex items-center justify-center"
                  style={{ width: "24px", height: "24px" }}
                >
                  DB
                </div>
              </div>
              <div className="text-xs text-gray-800 font-medium">
                Server PC with software
              </div>
            </div>
          </div>
        </div>

        {/* Torpedo Car - Center */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="text-center mb-4">
            <div className="text-base font-bold text-gray-800">Torpedo Car</div>
            <div className="text-xs text-red-700 font-semibold mt-1">
              Temp: {systemStatus.torpedoCar.temperature.toFixed(0)}°C |
              Position: {systemStatus.torpedoCar.position}
            </div>
          </div>
          <div className="flex items-center">
            {/* Left track */}
            <div
              className="bg-gray-700 flex items-center justify-center border-t-2 border-b-2 border-gray-800"
              style={{ height: "35px", width: "80px" }}
            >
              <div className="flex space-x-1">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-6 bg-gray-400 border-l border-gray-600"
                  ></div>
                ))}
              </div>
            </div>

            {/* Red square indicator */}
            <div
              className="bg-red-600 border-2 border-red-800 flex items-center justify-center"
              style={{ width: "35px", height: "35px" }}
            >
              <div
                className="bg-red-900 border border-red-700"
                style={{ width: "20px", height: "20px" }}
              ></div>
            </div>

            {/* Main torpedo body */}
            <div
              className="bg-gray-600 border-2 border-gray-800 relative flex items-center justify-center"
              style={{ height: "45px", width: "240px" }}
            >
              <div
                className={`border border-gray-700 ${
                  systemStatus.torpedoCar.temperature > 900
                    ? "bg-gradient-to-r from-red-500 via-orange-500 to-yellow-400"
                    : "bg-gradient-to-r from-orange-500 via-yellow-400 to-orange-300"
                }`}
                style={{ height: "30px", width: "180px" }}
              ></div>
            </div>

            {/* Right connector */}
            <div
              className="bg-gray-700 border-t-2 border-b-2 border-gray-800"
              style={{ width: "50px", height: "35px" }}
            ></div>

            {/* Right track */}
            <div
              className="bg-gray-700 flex items-center justify-center border-t-2 border-b-2 border-gray-800"
              style={{ height: "35px", width: "80px" }}
            >
              <div className="flex space-x-1">
                {[...Array(8)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-6 bg-gray-400 border-l border-gray-600"
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Second Thermal Camera - Bottom Left */}
        <div className="absolute bottom-12 left-1/4">
          <div
            className="border-2 border-gray-800 bg-gray-50 p-4"
            style={{ width: "140px" }}
          >
            <div className="text-xs font-medium text-gray-800 mb-3 text-center">
              Thermal Camera
            </div>
            <div
              className="bg-gray-200 border border-gray-500 mx-auto mb-3 flex items-center justify-center"
              style={{ width: "60px", height: "70px" }}
            >
              <div
                className={`border border-gray-800 ${
                  systemStatus.thermalCamera2.alert
                    ? "bg-red-600"
                    : "bg-gray-700"
                }`}
                style={{ width: "40px", height: "50px" }}
              ></div>
            </div>
            <div className="text-center">
              <div
                className={`text-white text-xs px-2 py-1 font-medium ${
                  systemStatus.thermalCamera2.alert
                    ? "bg-red-600"
                    : "bg-orange-600"
                }`}
              >
                {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
              </div>
            </div>
          </div>
        </div>

        {/* Control Room - Bottom Right */}
        <div className="absolute bottom-12 right-12">
          <div
            className="border-4 border-gray-900 bg-white p-6"
            style={{ width: "320px", height: "240px" }}
          >
            {/* Status bar with search icon */}
            <div className="flex items-center mb-6">
              <div
                className="bg-gray-400 rounded-full border-2 border-gray-700 flex items-center justify-center"
                style={{ width: "30px", height: "30px" }}
              >
                <div className="text-gray-700 text-sm">🔍</div>
              </div>
              <div className="flex-1 mx-3">
                <div className="bg-gray-100 border-2 border-gray-400 rounded px-3 py-2 text-sm font-medium">
                  Status:{" "}
                  {systemStatus.alarmSystem.active
                    ? "ALARM ACTIVE"
                    : "System Normal"}
                </div>
              </div>
            </div>

            <div className="flex justify-between items-start mb-6">
              <div className="text-center">
                <div className="text-sm font-bold text-gray-800 mb-3">
                  ALARM/HOOTER
                </div>
                <button
                  onClick={() =>
                    setSystemStatus((prev) => ({
                      ...prev,
                      alarmSystem: {
                        ...prev.alarmSystem,
                        active: !prev.alarmSystem.active,
                      },
                    }))
                  }
                  className={`px-4 py-2 rounded font-bold text-sm ${
                    systemStatus.alarmSystem.active
                      ? "bg-red-600 text-white animate-pulse"
                      : "bg-orange-500 text-white"
                  }`}
                >
                  {systemStatus.alarmSystem.active ? "STOP" : "TRIGGER"}
                </button>
              </div>

              <div className="text-center">
                <div
                  className="bg-gray-300 border-2 border-gray-700 rounded p-3 mb-3 flex items-center justify-center"
                  style={{ width: "60px", height: "60px" }}
                >
                  <div
                    className={`rounded border border-gray-600 ${
                      systemStatus.serverPC.status === "online"
                        ? "bg-blue-500"
                        : "bg-red-500"
                    }`}
                    style={{ width: "30px", height: "30px" }}
                  ></div>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div
                    className="bg-gray-400 border border-gray-600"
                    style={{ width: "30px", height: "10px" }}
                  ></div>
                  <div
                    className={`rounded-full border-2 border-gray-600 ${
                      systemStatus.alarmSystem.active
                        ? "bg-red-500 animate-pulse"
                        : "bg-blue-500"
                    }`}
                    style={{ width: "24px", height: "24px" }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-sm font-bold text-gray-800 mb-1">
                CONTROL
                <br />
                ROOM
              </div>
              <div className="text-sm font-bold text-gray-800 mb-2">
                Operator
              </div>
              <div className="text-xs text-gray-600">
                Active Alerts:{" "}
                {(systemStatus.thermalCamera1.alert ? 1 : 0) +
                  (systemStatus.thermalCamera2.alert ? 1 : 0)}
              </div>
            </div>
          </div>
        </div>

        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          {/* Line from Server PC to Control Room */}
          <line
            x1="82%"
            y1="30%"
            x2="82%"
            y2="65%"
            stroke="#374151"
            strokeWidth="3"
          />
          {/* Line from Torpedo to Control Room */}
          <line
            x1="50%"
            y1="58%"
            x2="78%"
            y2="65%"
            stroke="#374151"
            strokeWidth="3"
          />
          {/* Dashed line from OCR Camera */}
          <line
            x1="20%"
            y1="40%"
            x2="35%"
            y2="50%"
            stroke="#6B7280"
            strokeWidth="2"
            strokeDasharray="5,5"
          />
        </svg>
      </div>
    </div>
  );
}

export default App;
