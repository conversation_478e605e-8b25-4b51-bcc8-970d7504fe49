import { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [systemStatus, setSystemStatus] = useState({
    thermalCamera1: { status: "active", temperature: 45.2, alert: false },
    thermalCamera2: { status: "active", temperature: 42.8, alert: false },
    ocrCamera: { status: "active", lastScan: "TRP-001", confidence: 98.5 },
    torpedoCar: { position: "Station A", speed: 0, temperature: 850 },
    serverPC: { status: "online", cpu: 45, memory: 67, storage: 78 },
    alarmSystem: { active: false, lastAlert: null },
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus((prev) => ({
        ...prev,
        thermalCamera1: {
          ...prev.thermalCamera1,
          temperature: 40 + Math.random() * 20,
          alert: Math.random() > 0.8,
        },
        thermalCamera2: {
          ...prev.thermalCamera2,
          temperature: 38 + Math.random() * 15,
          alert: Math.random() > 0.85,
        },
        torpedoCar: {
          ...prev.torpedoCar,
          temperature: 800 + Math.random() * 100,
        },
        serverPC: {
          ...prev.serverPC,
          cpu: 30 + Math.random() * 40,
          memory: 50 + Math.random() * 30,
        },
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-200 p-4">
      {/* Title */}
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-black">
          Thermal Monitoring System
        </h1>
      </div>

      {/* Main Container - Exact replica of the diagram */}
      <div
        className="max-w-6xl mx-auto bg-white border-4 border-black p-8 relative"
        style={{ minHeight: "500px" }}
      >
        {/* OCR Camera - Top Left */}
        <div className="absolute top-8 left-8">
          <div
            className="border-2 border-black bg-white p-3"
            style={{ width: "120px" }}
          >
            <div className="text-xs font-semibold text-black mb-2">
              OCR Camera
              <br />
              for
              <br />
              identification
            </div>
            <div
              className="bg-gray-300 border-2 border-gray-600 mx-auto mb-2 flex items-center justify-center"
              style={{ width: "50px", height: "60px" }}
            >
              <div
                className="bg-gray-600"
                style={{ width: "30px", height: "40px" }}
              ></div>
            </div>
            <div className="text-center">
              <div className="bg-blue-500 text-white text-xs px-1 rounded inline-block">
                {systemStatus.ocrCamera.lastScan}
              </div>
            </div>
            {/* Wireless signal */}
            <div className="text-center mt-1">
              <div className="text-green-500 text-sm">📶</div>
            </div>
          </div>
        </div>

        {/* Thermal Camera - Top Center */}
        <div className="absolute top-8 left-1/2 transform -translate-x-1/2">
          <div
            className="border-2 border-black bg-white p-3"
            style={{ width: "120px" }}
          >
            <div className="text-xs font-semibold text-black mb-2 text-center">
              Thermal Camera
            </div>
            <div
              className="bg-gray-300 border-2 border-gray-600 mx-auto mb-2 flex items-center justify-center"
              style={{ width: "50px", height: "60px" }}
            >
              <div
                className={`${
                  systemStatus.thermalCamera1.alert
                    ? "bg-red-600"
                    : "bg-gray-600"
                }`}
                style={{ width: "30px", height: "40px" }}
              ></div>
            </div>
            <div className="text-center">
              <div
                className={`text-white text-xs px-1 rounded inline-block ${
                  systemStatus.thermalCamera1.alert
                    ? "bg-red-600"
                    : "bg-orange-500"
                }`}
              >
                {systemStatus.thermalCamera1.temperature.toFixed(1)}°C
              </div>
            </div>
          </div>
        </div>

        {/* Server PC - Top Right */}
        <div className="absolute top-8 right-8">
          <div
            className="border-2 border-black bg-white p-3"
            style={{ width: "140px" }}
          >
            <div
              className="bg-gray-200 border border-gray-400 mx-auto p-2 text-center"
              style={{ width: "120px", height: "80px" }}
            >
              <div
                className="bg-blue-500 text-white text-xs font-bold mx-auto mb-1 flex items-center justify-center"
                style={{ width: "20px", height: "20px" }}
              >
                DB
              </div>
              <div className="text-xs text-black">Server PC with software</div>
              <div className="text-xs text-gray-600 mt-1">
                CPU: {systemStatus.serverPC.cpu.toFixed(0)}%
              </div>
            </div>
          </div>
        </div>

        {/* Torpedo Car - Center */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="text-center mb-2">
            <div className="text-sm font-semibold text-black">Torpedo Car</div>
            <div className="text-xs text-red-600 font-semibold">
              Temp: {systemStatus.torpedoCar.temperature.toFixed(0)}°C |
              Position: {systemStatus.torpedoCar.position}
            </div>
          </div>
          <div className="flex items-center">
            {/* Left track */}
            <div
              className="bg-gray-600 flex items-center justify-center"
              style={{ height: "30px", width: "60px" }}
            >
              <div className="flex space-x-1">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="w-1 h-5 bg-gray-300"></div>
                ))}
              </div>
            </div>

            {/* Red square indicator */}
            <div
              className="bg-red-600 flex items-center justify-center"
              style={{ width: "30px", height: "30px" }}
            >
              <div
                className="bg-red-800 rounded"
                style={{ width: "15px", height: "15px" }}
              ></div>
            </div>

            {/* Main torpedo body */}
            <div
              className="bg-gray-500 relative flex items-center justify-center"
              style={{ height: "40px", width: "200px" }}
            >
              <div
                className={`${
                  systemStatus.torpedoCar.temperature > 900
                    ? "bg-gradient-to-r from-red-500 to-orange-600"
                    : "bg-gradient-to-r from-orange-400 to-yellow-500"
                }`}
                style={{ height: "25px", width: "140px" }}
              ></div>
            </div>

            {/* Right connector */}
            <div
              className="bg-gray-600"
              style={{ width: "40px", height: "30px" }}
            ></div>

            {/* Right track */}
            <div
              className="bg-gray-600 flex items-center justify-center"
              style={{ height: "30px", width: "60px" }}
            >
              <div className="flex space-x-1">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="w-1 h-5 bg-gray-300"></div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Second Thermal Camera - Bottom Left */}
        <div className="absolute bottom-8 left-1/3">
          <div
            className="border-2 border-black bg-white p-3"
            style={{ width: "120px" }}
          >
            <div className="text-xs font-semibold text-black mb-2 text-center">
              Thermal Camera
            </div>
            <div
              className="bg-gray-300 border-2 border-gray-600 mx-auto mb-2 flex items-center justify-center"
              style={{ width: "50px", height: "60px" }}
            >
              <div
                className={`${
                  systemStatus.thermalCamera2.alert
                    ? "bg-red-600"
                    : "bg-gray-600"
                }`}
                style={{ width: "30px", height: "40px" }}
              ></div>
            </div>
            <div className="text-center">
              <div
                className={`text-white text-xs px-1 rounded inline-block ${
                  systemStatus.thermalCamera2.alert
                    ? "bg-red-600"
                    : "bg-orange-500"
                }`}
              >
                {systemStatus.thermalCamera2.temperature.toFixed(1)}°C
              </div>
            </div>
          </div>
        </div>

        {/* Control Room - Bottom Right */}
        <div className="absolute bottom-8 right-8">
          <div
            className="border-4 border-black bg-white p-4"
            style={{ width: "280px", height: "200px" }}
          >
            {/* Search bar */}
            <div className="flex items-center mb-4">
              <div
                className="bg-gray-300 rounded-full border-2 border-gray-600"
                style={{ width: "25px", height: "25px" }}
              ></div>
              <div className="flex-1 mx-2">
                <div className="bg-gray-100 border border-gray-400 rounded px-2 py-1 text-xs">
                  Status:{" "}
                  {systemStatus.alarmSystem.active
                    ? "ALARM ACTIVE"
                    : "System Normal"}
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center mb-4">
              <div className="text-center">
                <div className="text-xs font-semibold text-black mb-2">
                  ALARM/HOOTER
                </div>
                <button
                  onClick={() =>
                    setSystemStatus((prev) => ({
                      ...prev,
                      alarmSystem: {
                        ...prev.alarmSystem,
                        active: !prev.alarmSystem.active,
                      },
                    }))
                  }
                  className={`px-3 py-1 rounded text-xs font-bold ${
                    systemStatus.alarmSystem.active
                      ? "bg-red-600 text-white animate-pulse"
                      : "bg-orange-500 text-white"
                  }`}
                >
                  {systemStatus.alarmSystem.active ? "STOP" : "TRIGGER"}
                </button>
              </div>

              <div className="text-center">
                <div
                  className="bg-gray-200 border-2 border-gray-600 rounded p-2 mb-2 flex items-center justify-center"
                  style={{ width: "50px", height: "50px" }}
                >
                  <div
                    className={`rounded ${
                      systemStatus.serverPC.status === "online"
                        ? "bg-blue-400"
                        : "bg-red-400"
                    }`}
                    style={{ width: "25px", height: "25px" }}
                  ></div>
                </div>
                <div className="flex items-center justify-center space-x-1">
                  <div
                    className="bg-gray-300 rounded"
                    style={{ width: "25px", height: "8px" }}
                  ></div>
                  <div
                    className={`rounded-full ${
                      systemStatus.alarmSystem.active
                        ? "bg-red-400 animate-pulse"
                        : "bg-blue-400"
                    }`}
                    style={{ width: "20px", height: "20px" }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-xs font-semibold text-black">
                CONTROL
                <br />
                ROOM
              </div>
              <div className="text-xs font-semibold text-black mt-1">
                Operator
              </div>
              <div className="text-xs text-gray-600 mt-1">
                Active Alerts:{" "}
                {(systemStatus.thermalCamera1.alert ? 1 : 0) +
                  (systemStatus.thermalCamera2.alert ? 1 : 0)}
              </div>
            </div>
          </div>
        </div>

        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          {/* Line from Server PC to Control Room */}
          <line
            x1="80%"
            y1="25%"
            x2="80%"
            y2="70%"
            stroke="black"
            strokeWidth="2"
          />
          {/* Line from Torpedo to Control Room */}
          <line
            x1="50%"
            y1="55%"
            x2="75%"
            y2="70%"
            stroke="black"
            strokeWidth="2"
          />
        </svg>
      </div>
    </div>
  );
}

export default App;
