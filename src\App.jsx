import { useState, useEffect } from "react";
import "./App.css";

function App() {
  const [systemStatus, setSystemStatus] = useState({
    thermalCamera1: { status: "active", temperature: 45.2, alert: false },
    thermalCamera2: { status: "active", temperature: 42.8, alert: false },
    ocrCamera: { status: "active", lastScan: "TRP-001", confidence: 98.5 },
    torpedoCar: { position: "Station A", speed: 0, temperature: 850 },
    serverPC: { status: "online", cpu: 45, memory: 67, storage: 78 },
    alarmSystem: { active: false, lastAlert: null },
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus((prev) => ({
        ...prev,
        thermalCamera1: {
          ...prev.thermalCamera1,
          temperature: 40 + Math.random() * 20,
          alert: Math.random() > 0.8,
        },
        thermalCamera2: {
          ...prev.thermalCamera2,
          temperature: 38 + Math.random() * 15,
          alert: Math.random() > 0.85,
        },
        torpedoCar: {
          ...prev.torpedoCar,
          temperature: 800 + Math.random() * 100,
        },
        serverPC: {
          ...prev.serverPC,
          cpu: 30 + Math.random() * 40,
          memory: 50 + Math.random() * 30,
        },
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-white p-8">
      {/* Main Container - Exact replica of the diagram */}
      <div
        className="max-w-6xl mx-auto bg-white border-4 border-gray-600 relative"
        style={{ minHeight: "500px", padding: "20px" }}
      >
        {/* OCR Camera - Top Left */}
        <div className="absolute top-4 left-4">
          <div
            className="text-xs font-medium text-black mb-2 leading-tight"
            style={{ width: "100px" }}
          >
            OCR Camera
            <br />
            for
            <br />
            identification
          </div>
          <div className="relative">
            {/* Camera device */}
            <div
              className="bg-gray-300 border-2 border-gray-600 mx-auto mb-2 flex items-center justify-center relative"
              style={{ width: "50px", height: "60px" }}
            >
              <div
                className="bg-gray-700 border border-gray-800 relative"
                style={{ width: "35px", height: "45px" }}
              >
                {/* Diagonal stripes pattern */}
                <div className="absolute inset-0">
                  <div
                    className="w-full h-full"
                    style={{
                      background:
                        "repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(0,0,0,0.2) 3px, rgba(0,0,0,0.2) 6px)",
                    }}
                  ></div>
                </div>
              </div>
            </div>
            {/* Wireless signal with arrow */}
            <div className="absolute -right-8 top-6">
              <div className="text-green-600 text-lg">📶</div>
            </div>
            {/* Dashed arrow pointing down */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-16">
              <svg width="2" height="60" className="stroke-gray-600">
                <line
                  x1="1"
                  y1="0"
                  x2="1"
                  y2="60"
                  strokeDasharray="3,3"
                  strokeWidth="2"
                />
                <polygon points="1,55 5,50 1,60 -3,50" fill="#666" />
              </svg>
            </div>
          </div>
        </div>

        {/* Thermal Camera - Top Center */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
          <div className="text-center">
            <div className="text-xs font-medium text-black mb-2">
              Thermal Camera
            </div>
            {/* Camera mount structure */}
            <div
              className="relative mx-auto"
              style={{ width: "80px", height: "120px" }}
            >
              {/* Base mount */}
              <div
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-gray-400 border-2 border-gray-600"
                style={{ width: "60px", height: "20px" }}
              ></div>
              {/* Vertical support */}
              <div
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-400 border-2 border-gray-600"
                style={{ width: "8px", height: "40px" }}
              ></div>
              {/* Camera housing */}
              <div
                className="absolute top-0 left-1/2 transform -translate-x-1/2 bg-gray-300 border-2 border-gray-600 flex items-center justify-center"
                style={{ width: "50px", height: "60px" }}
              >
                <div
                  className={`border border-gray-800 ${
                    systemStatus.thermalCamera1.alert
                      ? "bg-red-600"
                      : "bg-gray-700"
                  }`}
                  style={{ width: "35px", height: "45px" }}
                >
                  {/* Diagonal stripes pattern */}
                  <div
                    className="w-full h-full"
                    style={{
                      background:
                        "repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,0.2) 3px, rgba(255,255,255,0.2) 6px)",
                    }}
                  ></div>
                </div>
              </div>
              {/* Mounting arm */}
              <div
                className="absolute top-12 -right-4 bg-gray-400 border border-gray-600"
                style={{ width: "25px", height: "8px" }}
              ></div>
            </div>
          </div>
        </div>

        {/* Server PC - Top Right */}
        <div className="absolute top-4 right-4">
          <div
            className="border-2 border-gray-600 bg-white p-3"
            style={{ width: "180px", height: "100px" }}
          >
            <div className="flex items-center justify-center h-full">
              {/* Monitor */}
              <div
                className="bg-gray-300 border-2 border-gray-600 p-2 mr-2"
                style={{ width: "60px", height: "50px" }}
              >
                <div className="bg-gray-700 border border-gray-800 w-full h-6 mb-1 flex items-center justify-center">
                  <div className="bg-blue-600 text-white text-xs font-bold px-1">
                    DB
                  </div>
                </div>
                <div className="bg-gray-500 w-full h-2"></div>
              </div>
              {/* CPU Tower */}
              <div
                className="bg-gray-400 border-2 border-gray-600 mr-2"
                style={{ width: "25px", height: "60px" }}
              >
                <div className="bg-gray-600 w-full h-2 mt-1"></div>
                <div className="bg-gray-600 w-full h-2 mt-1"></div>
              </div>
              {/* Text */}
              <div className="text-xs text-black font-medium leading-tight">
                Server PC with
                <br />
                software
              </div>
            </div>
          </div>
        </div>

        {/* Torpedo Car - Center */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="text-center mb-2">
            <div className="text-sm font-bold text-black">Torpedo Car</div>
          </div>
          <div className="flex items-center">
            {/* Left track */}
            <div
              className="bg-gray-600 border-2 border-gray-800"
              style={{ height: "30px", width: "80px" }}
            >
              <div className="flex justify-between items-center h-full px-1">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="w-0.5 h-4 bg-gray-400"></div>
                ))}
              </div>
            </div>

            {/* Red square indicator */}
            <div
              className="bg-red-600 border-2 border-red-800 flex items-center justify-center relative"
              style={{ width: "30px", height: "30px" }}
            >
              <div
                className="bg-red-900 rounded"
                style={{ width: "12px", height: "12px" }}
              ></div>
              {/* Spiral symbol */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-white text-xs font-bold">⟲</div>
              </div>
            </div>

            {/* Main torpedo body */}
            <div
              className="bg-gray-500 border-2 border-gray-700 relative flex items-center justify-center"
              style={{ height: "40px", width: "200px" }}
            >
              {/* Orange heating element */}
              <div
                className={`${
                  systemStatus.torpedoCar.temperature > 900
                    ? "bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500"
                    : "bg-gradient-to-r from-orange-400 via-yellow-300 to-orange-400"
                } border border-gray-600`}
                style={{ height: "25px", width: "150px" }}
              ></div>
              {/* Temperature display */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-red-600 font-semibold">
                {systemStatus.torpedoCar.temperature.toFixed(0)}°C
              </div>
            </div>

            {/* Right connector */}
            <div
              className="bg-gray-500 border-2 border-gray-700"
              style={{ width: "40px", height: "30px" }}
            ></div>

            {/* Right track */}
            <div
              className="bg-gray-600 border-2 border-gray-800"
              style={{ height: "30px", width: "80px" }}
            >
              <div className="flex justify-between items-center h-full px-1">
                {[...Array(12)].map((_, i) => (
                  <div key={i} className="w-0.5 h-4 bg-gray-400"></div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Second Thermal Camera - Bottom Left */}
        <div className="absolute bottom-4 left-1/4">
          <div className="text-center">
            <div className="text-xs font-medium text-black mb-2">
              Thermal Camera
            </div>
            {/* Camera mount structure */}
            <div
              className="relative mx-auto"
              style={{ width: "80px", height: "120px" }}
            >
              {/* Base mount */}
              <div
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-gray-400 border-2 border-gray-600"
                style={{ width: "60px", height: "20px" }}
              ></div>
              {/* Vertical support */}
              <div
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-400 border-2 border-gray-600"
                style={{ width: "8px", height: "40px" }}
              ></div>
              {/* Camera housing */}
              <div
                className="absolute top-0 left-1/2 transform -translate-x-1/2 bg-gray-300 border-2 border-gray-600 flex items-center justify-center"
                style={{ width: "50px", height: "60px" }}
              >
                <div
                  className={`border border-gray-800 ${
                    systemStatus.thermalCamera2.alert
                      ? "bg-red-600"
                      : "bg-gray-700"
                  }`}
                  style={{ width: "35px", height: "45px" }}
                >
                  {/* Diagonal stripes pattern */}
                  <div
                    className="w-full h-full"
                    style={{
                      background:
                        "repeating-linear-gradient(45deg, transparent, transparent 3px, rgba(255,255,255,0.2) 3px, rgba(255,255,255,0.2) 6px)",
                    }}
                  ></div>
                </div>
              </div>
              {/* Mounting arm */}
              <div
                className="absolute top-12 -right-4 bg-gray-400 border border-gray-600"
                style={{ width: "25px", height: "8px" }}
              ></div>
            </div>
          </div>
        </div>

        {/* Control Room - Bottom Right */}
        <div className="absolute bottom-12 right-12">
          <div
            className="border-4 border-gray-900 bg-white p-6"
            style={{ width: "320px", height: "240px" }}
          >
            {/* Status bar with search icon */}
            <div className="flex items-center mb-6">
              <div
                className="bg-gray-400 rounded-full border-2 border-gray-700 flex items-center justify-center"
                style={{ width: "30px", height: "30px" }}
              >
                <div className="text-gray-700 text-sm">🔍</div>
              </div>
              <div className="flex-1 mx-3">
                <div className="bg-gray-100 border-2 border-gray-400 rounded px-3 py-2 text-sm font-medium">
                  Status:{" "}
                  {systemStatus.alarmSystem.active
                    ? "ALARM ACTIVE"
                    : "System Normal"}
                </div>
              </div>
            </div>

            <div className="flex justify-between items-start mb-6">
              <div className="text-center">
                <div className="text-sm font-bold text-gray-800 mb-3">
                  ALARM/HOOTER
                </div>
                <button
                  onClick={() =>
                    setSystemStatus((prev) => ({
                      ...prev,
                      alarmSystem: {
                        ...prev.alarmSystem,
                        active: !prev.alarmSystem.active,
                      },
                    }))
                  }
                  className={`px-4 py-2 rounded font-bold text-sm ${
                    systemStatus.alarmSystem.active
                      ? "bg-red-600 text-white animate-pulse"
                      : "bg-orange-500 text-white"
                  }`}
                >
                  {systemStatus.alarmSystem.active ? "STOP" : "TRIGGER"}
                </button>
              </div>

              <div className="text-center">
                <div
                  className="bg-gray-300 border-2 border-gray-700 rounded p-3 mb-3 flex items-center justify-center"
                  style={{ width: "60px", height: "60px" }}
                >
                  <div
                    className={`rounded border border-gray-600 ${
                      systemStatus.serverPC.status === "online"
                        ? "bg-blue-500"
                        : "bg-red-500"
                    }`}
                    style={{ width: "30px", height: "30px" }}
                  ></div>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div
                    className="bg-gray-400 border border-gray-600"
                    style={{ width: "30px", height: "10px" }}
                  ></div>
                  <div
                    className={`rounded-full border-2 border-gray-600 ${
                      systemStatus.alarmSystem.active
                        ? "bg-red-500 animate-pulse"
                        : "bg-blue-500"
                    }`}
                    style={{ width: "24px", height: "24px" }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-sm font-bold text-gray-800 mb-1">
                CONTROL
                <br />
                ROOM
              </div>
              <div className="text-sm font-bold text-gray-800 mb-2">
                Operator
              </div>
              <div className="text-xs text-gray-600">
                Active Alerts:{" "}
                {(systemStatus.thermalCamera1.alert ? 1 : 0) +
                  (systemStatus.thermalCamera2.alert ? 1 : 0)}
              </div>
            </div>
          </div>
        </div>

        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          {/* Line from Server PC to Control Room */}
          <line
            x1="82%"
            y1="30%"
            x2="82%"
            y2="65%"
            stroke="#374151"
            strokeWidth="3"
          />
          {/* Line from Torpedo to Control Room */}
          <line
            x1="50%"
            y1="58%"
            x2="78%"
            y2="65%"
            stroke="#374151"
            strokeWidth="3"
          />
          {/* Dashed line from OCR Camera */}
          <line
            x1="20%"
            y1="40%"
            x2="35%"
            y2="50%"
            stroke="#6B7280"
            strokeWidth="2"
            strokeDasharray="5,5"
          />
        </svg>
      </div>
    </div>
  );
}

export default App;
